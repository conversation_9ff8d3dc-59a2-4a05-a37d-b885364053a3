package my.case0.controller;

import cn.idev.excel.FastExcel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.case0.entity.Case;
import my.case0.interceptor.Admin;
import my.case0.interceptor.Login;
import my.case0.model.BatchDeleteReq;
import my.case0.model.CommResp;
import my.case0.model.UserInfo;
import my.case0.model.item.*;
import my.case0.service.CaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


@Tag(name = "案件管理接口", description = "案件管理相关的API接口")
@RestController
@RequestMapping("api/case")
@Slf4j
public class CaseController {

    @Autowired
    private CaseService caseService;

    @Operation(summary = "案件列表查询", description = "分页查询案件列表")
    @Login
    @PostMapping("/list")
    public CommResp<ListResp> list(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "分页请求参数") @RequestBody ListReq req
    ) {
        if (userInfo.getAdminRole() == 2) {
            req.setAdminId(userInfo.getAdminId());
        }
        return caseService.list(req);
    }

    @Operation(summary = "案件列表导出")
    @Login
    @PostMapping("/export")
    public void export(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "分页请求参数") @RequestBody ListReq req,
            HttpServletResponse response
    ) throws IOException {
        req.setPageNum(1);
        req.setPageSize(10000);
        List<ItemResp> respList = caseService.getItemList(req);
        // 导出Excel
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("案件列表", StandardCharsets.UTF_8) + ".xlsx");
        FastExcel.write(response.getOutputStream(), ItemResp.class).sheet("案件列表").doWrite(respList);
    }

    @Operation(summary = "案件详情查询", description = "查询案件详情")
    @Login
    @GetMapping("/detail")
    public CommResp<DetailResp> detail(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件ID") @RequestParam("caseId") Integer caseId,
            @Parameter(description = "查询类型 1-上一个 2-下一个") @RequestParam(value = "type", required = false) Byte type
    ) {
        return caseService.detail(userInfo, caseId, type);
    }

    @Operation(summary = "新增/编辑案件", description = "新增/编辑案件信息")
    @Admin
    @PostMapping("/save")
    public CommResp<Case> save(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件保存请求参数") @RequestBody SaveReq req
    ) {
        return caseService.save(req);
    }

    @Operation(summary = "修改案件状态", description = "修改案件状态")
    @Admin
    @PostMapping("/updateStatus")
    public CommResp<Void> updateStatus(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件ID") @RequestParam("caseId") Integer caseId,
            @Parameter(description = "案件状态 1-回退 2-完结") @RequestParam("caseStatus") Byte status
    ) {
        return caseService.updateStatus(caseId, status);
    }

    @Operation(summary = "批量导入案件", description = "通过Excel文件批量导入案件信息")
    @Admin
    @PostMapping("/import")
    public CommResp<String> importCases(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "clientId") @RequestParam("clientId") Integer clientId,
            @Parameter(description = "Excel文件") @RequestParam("file") MultipartFile file
    ) {
        return caseService.importCases(clientId, file);
    }

    @Operation(summary = "分配案件", description = "将案件批量分配给普通管理员")
    @Admin
    @PostMapping("/assign")
    public CommResp<Void> assign(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件分配请求参数") @RequestBody AssignReq req
    ) {
        return caseService.assign(userInfo, req);
    }

    @Operation(summary = "分配所有未分配的案件")
    @Admin
    @PostMapping("/assignAll")
    public CommResp<Void> assignAll(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件分配请求参数") @RequestBody AssignReq req
    ) {
        return caseService.assignAll(userInfo, req);
    }

    @Operation(summary = "保存操作记录", description = "保存案件操作记录")
    @Login
    @PostMapping("/saveLog")
    public CommResp<Void> saveLog(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "操作记录保存请求参数") @RequestBody LogReq req
    ) {
        return caseService.saveLog(userInfo, req);
    }

    @Operation(summary = "案件操作记录", description = "根据案件ID查询案件操作记录列表")
    @Login
    @GetMapping("/logs/{caseId}")
    public CommResp<List<LogResp>> getCaseLogs(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件ID") @PathVariable("caseId") Integer caseId
    ) {
        return caseService.getCaseLogs(userInfo, caseId);
    }

    @Operation(summary = "批量逻辑删除案件", description = "将案件状态设置为删除状态")
    @Admin
    @PostMapping("/delete")
    public CommResp<Void> delete(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件ID列表") @RequestBody BatchDeleteReq req
    ) {
        return caseService.batchDelete(userInfo, req.getIds());
    }

    @Operation(summary = "批量重置案件为未分配状态", description = "将案件状态设置为未分配状态")
    @Admin
    @PostMapping("/reset")
    public CommResp<Void> reset(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "案件ID列表") @RequestBody BatchDeleteReq req
    ) {
        return caseService.batchReset(userInfo, req.getIds());
    }
}