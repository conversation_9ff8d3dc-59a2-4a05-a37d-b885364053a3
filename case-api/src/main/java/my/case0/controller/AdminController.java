package my.case0.controller;

import com.github.benmanes.caffeine.cache.Cache;
import com.google.code.kaptcha.Producer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.case0.interceptor.Admin;
import my.case0.interceptor.Login;
import my.case0.model.CommResp;
import my.case0.model.UserInfo;
import my.case0.model.admin.*;
import my.case0.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.List;

/**
 * 管理员控制器
 */
@Tag(name = "管理员接口", description = "管理员相关的API接口")
@RestController
@RequestMapping("api/admin")
@Slf4j
public class AdminController {

    @Autowired
    private AdminService adminService;
    @Autowired
    private Producer producer;
    @Autowired
    @Qualifier("loginCache")
    private Cache<String, String> loginCache;

    @Operation(summary = "获取图片验证码", description = "生成数学运算验证码图片")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功返回验证码图片",
                    content = @Content(mediaType = "image/jpeg"))
    })
    @GetMapping("image/code")
    public void imageCode(
            @Parameter(description = "验证码标识", required = true, example = "captcha123")
            @RequestParam("code") String code,
            HttpServletResponse response
    ) throws IOException {
        response.setContentType("image/jpeg");
        //生成文字验证码
        String text = producer.createText();
        //个位数字相加
        String s1 = text.substring(0, 2);
        String s2 = text.substring(2, 4);
        int count = Integer.parseInt(s1) + Integer.parseInt(s2);
        //生成图片验证码
        BufferedImage image = producer.createImage(s1 + "+" + s2 + "=?");
        ImageIO.write(image, "jpg", response.getOutputStream());
        loginCache.put(code, String.valueOf(count));
    }

    @Operation(summary = "管理员登录", description = "管理员账号密码登录，需要验证码")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(schema = @Schema(implementation = LoginResp.class))),
            @ApiResponse(responseCode = "2001", description = "登录失败"),
            @ApiResponse(responseCode = "2003", description = "用户不存在"),
            @ApiResponse(responseCode = "2004", description = "密码错误超过当日上限"),
            @ApiResponse(responseCode = "2006", description = "用户被冻结"),
            @ApiResponse(responseCode = "2008", description = "密码错误")
    })
    @PostMapping("login")
    public CommResp<LoginResp> login(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "登录请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = LoginReq.class))
            )
            @RequestBody LoginReq req
    ) {
        return adminService.login(req);
    }

    @Operation(summary = "管理员列表", description = "分页查询管理员列表，支持按昵称、账号、角色、状态筛选")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(schema = @Schema(implementation = ListResp.class))),
            @ApiResponse(responseCode = "444", description = "未登录或登录过期"),
            @ApiResponse(responseCode = "445", description = "没有访问权限")
    })
    @GetMapping("list")
    @Admin
    public CommResp<List<ListResp>> list(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @Parameter(description = "查询参数") ListReq req
    ) {
        return adminService.list(req);
    }
    
    @Operation(summary = "保存管理员", description = "新增或编辑管理员信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "保存成功"),
            @ApiResponse(responseCode = "444", description = "未登录或登录过期"),
            @ApiResponse(responseCode = "445", description = "没有访问权限")
    })
    @PostMapping("save")
    @Admin
    public CommResp<?> save(
            @Parameter(hidden = true) @Login UserInfo userInfo,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "管理员信息",
                    required = true,
                    content = @Content(schema = @Schema(implementation = SaveReq.class))
            )
            @RequestBody SaveReq req
    ) {
        return adminService.save(req);
    }

    @Operation(summary = "普通管理员列表", description = "查询所有普通管理员")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(schema = @Schema(implementation = ListResp.class)))
    })
    @GetMapping("common/list")
    @Admin
    public CommResp<List<ItemResp>> commonList() {
        return adminService.commonList();
    }
}