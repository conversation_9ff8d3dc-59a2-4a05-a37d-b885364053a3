package my.case0.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import my.case0.model.CommResp;
import my.case0.model.IdReq;
import my.case0.model.client.ListResp;
import my.case0.model.client.SaveReq;
import my.case0.service.ClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "客户管理")
@RestController
@RequestMapping("api/client")
public class ClientController {

    @Autowired
    private ClientService clientService;

    @Operation(summary = "保存客户")
    @PostMapping("/save")
    public CommResp<?> save(
            @RequestBody SaveReq req
    ) {
        return clientService.saveClient(req);
    }

    @Operation(summary = "删除客户")
    @PostMapping("/delete")
    public CommResp<?> delete(
            @RequestBody IdReq req
    ) {
        return clientService.deleteClient(req.getId());
    }

    @Operation(summary = "所有客户（下拉列表）")
    @GetMapping("/list")
    public CommResp<List<ListResp>> items() {
        return clientService.listClients();
    }
}