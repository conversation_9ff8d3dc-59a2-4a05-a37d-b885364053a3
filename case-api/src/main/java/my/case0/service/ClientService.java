package my.case0.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import my.case0.entity.Client;
import my.case0.mapper.ClientMapper;
import my.case0.model.CommResp;
import my.case0.model.RespMeta;
import my.case0.model.client.ListResp;
import my.case0.model.client.SaveReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ClientService {

    @Autowired
    private ClientMapper clientMapper;

    public CommResp<?> saveClient(SaveReq req) {
        Client client = new Client();
        client.setId(req.getId());
        client.setClientName(req.getName());
        
        if (client.getId() == null) {
            // 新增
            client.setClientStatus((byte) 1); // 默认状态为正常
            clientMapper.insert(client);
        } else {
            // 更新
            Client existing = clientMapper.selectById(client.getId());
            if (existing == null) {
                return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "客户不存在");
            }
            // 保留状态字段
            client.setClientStatus(null);
            clientMapper.updateById(client);
        }
        return CommResp.success();
    }

    public CommResp<List<ListResp>> listClients() {
        LambdaQueryWrapper<Client> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Client::getClientStatus, 1);
        queryWrapper.orderByAsc(Client::getId);

        List<Client> list = clientMapper.selectList(queryWrapper);
        List<ListResp> records = list.stream()
                .map(client -> {
                    ListResp resp = new ListResp();
                    resp.setId(client.getId());
                    resp.setClientName(client.getClientName());
                    return resp;
                })
                .collect(Collectors.toList());
        return CommResp.success(records);
    }

    public CommResp<?> deleteClient(Long id) {
        Client client = clientMapper.selectById(id);
        if (client == null) {
            return CommResp.warning("客户不存在");
        }
        client.setClientStatus((byte) 2); // 设置为删除状态
        clientMapper.updateById(client);
        return CommResp.success();
    }
}