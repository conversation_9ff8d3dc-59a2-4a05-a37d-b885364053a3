package my.case0.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import my.case0.entity.Admin;
import my.case0.mapper.AdminMapper;
import my.case0.model.CommResp;
import my.case0.model.RespMeta;
import my.case0.model.admin.*;
import my.case0.util.JwtUtils;
import my.case0.util.StrUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminService {

    @Autowired
    private AdminMapper adminMapper;
    @Autowired
    @Qualifier("loginCache")
    private Cache<String, String> loginCache;

    public CommResp<LoginResp> login(LoginReq req) {
        // 校验参数
        if (!StringUtils.hasLength(req.getAccount()) 
                || !StringUtils.hasLength(req.getPassword())
                || !StringUtils.hasLength(req.getCode())) {
            return CommResp.warning(RespMeta.PARAM_ERROR);
        }
        
        // 校验验证码
        String code = loginCache.getIfPresent(req.getCode());
        if (!StringUtils.hasLength(code) || !code.equals(req.getVerifyCode())) {
            return CommResp.warning(RespMeta.LOGIN_FAIL.getStatus(), "验证码错误");
        }

        loginCache.invalidate(req.getCode());
        // 查询管理员账号
        Admin admin = adminMapper.selectOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getAccount, req.getAccount()));
        if (admin == null) {
            return CommResp.warning(RespMeta.NO_USER);
        }

        LocalDateTime lastLoginTime = admin.getLastLoginTime();
        // 检查登录错误次数
        if (lastLoginTime != null && LocalDate.now().equals(lastLoginTime.toLocalDate()) && admin.getPwdErrorNum() >= 5) {
            return CommResp.warning(RespMeta.REPEAT_PWD_ERROR);
        }

        admin.setLastLoginTime(LocalDateTime.now());
        // 校验密码
        String hashedPassword = JwtUtils.hashPwd(req.getPassword());
        if (!hashedPassword.equals(admin.getPassword())) {
            // 更新密码错误次数
            if (lastLoginTime == null || !LocalDate.now().equals(lastLoginTime.toLocalDate())) {
                admin.setPwdErrorNum(0);
            }
            admin.setPwdErrorNum(admin.getPwdErrorNum() + 1);
            adminMapper.updateById(admin);
            return CommResp.warning(RespMeta.PASSWORD_FAILED);
        }
        
        // 检查账户状态
        if (admin.getAdminStatus() != 1) {
            return CommResp.warning(RespMeta.USER_FROZEN);
        }
        
        // 重置密码错误次数
        admin.setPwdErrorNum(0);

        // 生成token
        String token = JwtUtils.createAdminToken(admin.getId(), admin.getAdminRole());
        
        // 构造返回结果
        LoginResp resp = new LoginResp();
        resp.setToken(token);
        resp.setAccount(admin.getAccount());
        resp.setNickname(admin.getNickname());
        resp.setAdminRole(admin.getAdminRole());
        
        // 更新最后登录时间
        adminMapper.updateById(admin);
        
        return CommResp.success(resp);
    }

    public CommResp<List<ListResp>> list(ListReq req) {
        // 创建分页对象
        Page<Admin> page = new Page<>(req.getPageNum(), req.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询必要的字段
        queryWrapper.select(Admin::getId, Admin::getNickname, Admin::getAccount, 
                Admin::getAdminRole, Admin::getAdminStatus, Admin::getCreatedTime, 
                Admin::getLastLoginTime);
        
        // 根据传入参数进行过滤查询
        if (StrUtils.isNotEmpty(req.getNickname())) {
            queryWrapper.like(Admin::getNickname, req.getNickname());
        }
        if (StrUtils.isNotEmpty(req.getAccount())) {
            queryWrapper.like(Admin::getAccount, req.getAccount());
        }
        if (req.getAdminRole() != null && req.getAdminRole() > 0) {
            queryWrapper.eq(Admin::getAdminRole, req.getAdminRole());
        }
        if (req.getAdminStatus() != null && req.getAdminStatus() > 0) {
            queryWrapper.eq(Admin::getAdminStatus, req.getAdminStatus());
        }
        
        queryWrapper.orderByDesc(Admin::getCreatedTime);
        
        // 执行分页查询
        IPage<Admin> result = adminMapper.selectPage(page, queryWrapper);
        
        // 转换结果
        List<ListResp> list = result.getRecords().stream().map(AdminConvertor::toListResp).collect(Collectors.toList());
        
        // 返回分页结果
        return CommResp.success(req.getPageNum(), req.getPageSize(), 
                result.getTotal(), list);
    }
    
    public CommResp<?> save(SaveReq req) {
        // 校验参数
        if (!StringUtils.hasLength(req.getNickname()) || !StringUtils.hasLength(req.getAccount())) {
            return CommResp.warning(RespMeta.PARAM_ERROR);
        }
        
        if (req.getAdminRole() == null || (req.getAdminRole() != 1 && req.getAdminRole() != 2)) {
            return CommResp.warning(RespMeta.PARAM_ERROR);
        }
        
        if (req.getAdminStatus() == null || (req.getAdminStatus() != 1 && req.getAdminStatus() != 2)) {
            return CommResp.warning(RespMeta.PARAM_ERROR);
        }
        
        // 检查账号是否已存在
        if (req.getId() == null) {
            LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Admin::getAccount, req.getAccount());
            Admin existing = adminMapper.selectOne(queryWrapper);
            if (existing != null) {
                return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "账号已存在");
            }
        }
        
        // 构造管理员实体
        Admin admin = new Admin();
        admin.setId(req.getId());
        admin.setNickname(req.getNickname());
        admin.setAccount(req.getAccount());
        admin.setAdminRole(req.getAdminRole());
        admin.setAdminStatus(req.getAdminStatus());
        
        // 处理密码
        if (req.getId() == null) {
            // 新增时必须提供密码
            if (!StringUtils.hasLength(req.getPassword())) {
                return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "新增管理员必须提供密码");
            }
            admin.setPassword(JwtUtils.hashPwd(req.getPassword()));
            admin.setCreatedTime(LocalDateTime.now());
            adminMapper.insert(admin);
        } else {
            // 编辑时密码可选
            if (StringUtils.hasLength(req.getPassword())) {
                admin.setPassword(JwtUtils.hashPwd(req.getPassword()));
            }
            adminMapper.updateById(admin);
        }
        
        return CommResp.success();
    }
    
    public CommResp<List<ItemResp>> commonList() {
        // 构建查询条件
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询必要的字段
        queryWrapper.select(Admin::getId, Admin::getNickname, Admin::getAccount);
        
        // 添加管理员状态为正常的条件
        queryWrapper.eq(Admin::getAdminStatus, 1).eq(Admin::getAdminRole, 2);
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Admin::getId);
        
        // 执行查询
        List<Admin> admins = adminMapper.selectList(queryWrapper);
        
        // 转换结果
        List<ItemResp> result = admins.stream()
                .map(AdminConvertor::toItemResp)
                .collect(Collectors.toList());
        
        return CommResp.success(result);
    }
}