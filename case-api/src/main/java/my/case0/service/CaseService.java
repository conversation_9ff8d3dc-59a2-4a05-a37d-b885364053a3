package my.case0.service;

import cn.idev.excel.FastExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import my.case0.entity.Admin;
import my.case0.entity.Case;
import my.case0.entity.Client;
import my.case0.entity.OperationLog;
import my.case0.mapper.*;
import my.case0.model.CommResp;
import my.case0.model.RespMeta;
import my.case0.model.UserInfo;
import my.case0.model.item.*;
import my.case0.util.DecimalUtils;
import my.case0.util.StrUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CaseService {

    @Autowired
    private CaseMapper caseMapper;
    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private AdminMapper adminMapper;
    @Autowired
    private ClientMapper clientMapper;

    /**
     * 案件列表查询
     *
     * @param req 分页请求参数
     * @return 案件列表分页结果
     */
    public CommResp<ListResp> list(ListReq req) {

        // 使用自定义SQL一次性查询统计信息
        CaseStatResult statResult = caseMapper.statCases(req);
        ListResp resp = new ListResp();
        resp.setTotalAmount(DecimalUtils.toString(statResult.getTotalAmount()));
        resp.setTotalSurplusAmount(DecimalUtils.toString(statResult.getTotalSurplusAmount()));
        if (statResult.getTotal() == 0 || statResult.getTotal() <= req.getOffset()) {
            return CommResp.success(req.getPageNum(), req.getPageSize(), statResult.getTotal(), resp);
        }
        resp.setItemList(getItemList(req));
        return CommResp.success(req.getPageNum(), req.getPageSize(), statResult.getTotal(), resp);
    }

    public List<ItemResp> getItemList(ListReq req) {
        List<Case> result = caseMapper.listCases(req);
        // 获取所有被分配的管理员ID
        List<Integer> adminIds = result.stream()
                .map(Case::getAdminId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询管理员名称
        Map<Integer, String> adminNameMap = new HashMap<>();
        if (!adminIds.isEmpty()) {
            LambdaQueryWrapper<Admin> adminQueryWrapper = new LambdaQueryWrapper<>();
            adminQueryWrapper.select(Admin::getId, Admin::getNickname);
            adminQueryWrapper.in(Admin::getId, adminIds);
            List<Admin> admins = adminMapper.selectList(adminQueryWrapper);
            adminNameMap = admins.stream()
                    .collect(Collectors.toMap(Admin::getId, Admin::getNickname));
        }
        // 批量查询委托方名称
        List<Integer> clientIds = result.stream()
                .map(Case::getClientId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<Integer, String> clientNameMap = new HashMap<>();
        if (!clientIds.isEmpty()) {
            LambdaQueryWrapper<Client> clientQueryWrapper = new LambdaQueryWrapper<>();
            clientQueryWrapper.select(Client::getId, Client::getClientName);
            clientQueryWrapper.in(Client::getId, clientIds);
            List<Client> clients = clientMapper.selectList(clientQueryWrapper);
            clientNameMap = clients.stream()
                    .collect(Collectors.toMap(Client::getId, Client::getClientName));
        }

        // 转换为Item列表
        List<ItemResp> itemList = new ArrayList<>();
        for (Case caseEntity : result) {
            ItemResp item = ItemConvertor.toListResp(caseEntity);
            String name = adminNameMap.get(caseEntity.getAdminId());
            item.setAssignUserName(name);
            item.setClientName(clientNameMap.get(caseEntity.getClientId()));
            itemList.add(item);
        }
        return itemList;
    }

    /**
     * 保存案件信息（新增或编辑）
     *
     * @param req 保存请求参数
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<Case> save(SaveReq req) {
        Case caseEntity = ItemConvertor.toCase(req);
        // 保存到数据库
        if (req.getId() != null) {
            caseMapper.updateById(caseEntity);
        } else {
            caseMapper.insert(caseEntity);
        }
        // 重新查询保存后的数据
        Case savedCase = caseMapper.selectById(caseEntity.getId());
        return CommResp.success(savedCase);
    }

    /**
     * 批量导入案件信息
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<String> importCases(Integer clientId, MultipartFile file) {
        try {
            // 使用fastexcel解析Excel文件
            List<SaveReq> importList = FastExcel.read(file.getInputStream())
                    .sheet(0)
                    .head(SaveReq.class)
                    .headRowNumber(1)
                    .doReadSync();

            // 转换并保存数据
            int successCount = 0;
            for (SaveReq req : importList) {
                Case caseEntity = ItemConvertor.toCase(req);
                // 判断是否已经存在
                LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(Case::getId);
                queryWrapper.eq(Case::getCaseId, req.getCaseId()).eq(Case::getClientId, clientId);
                Case existing = caseMapper.selectOne(queryWrapper);
                if (existing != null) {
                    caseEntity.setId(existing.getId());
                }
                caseEntity.setClientId(clientId);
                caseEntity.setCaseStatus((byte) 1);
                caseMapper.insertOrUpdate(caseEntity);
                successCount++;
            }
            return CommResp.success("成功导入" + successCount + "条案件数据");
        } catch (Exception e) {
            log.error("导入案件数据失败", e);
            return CommResp.warning(1001, "导入案件数据失败");
        }
    }

    /**
     * 分配案件给管理员
     *
     * @param req 分配请求参数
     * @return 分配结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<Void> assign(UserInfo userInfo, AssignReq req) {
        // 参数校验
        if (req.getCaseIds() == null || req.getCaseIds().isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件ID列表不能为空");
        }

        if (req.getAdminIds() == null || req.getAdminIds().isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "管理员ID列表不能为空");
        }

        if (req.getType() == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "分配类型不能为空");
        }

        // 查询所有相关案件信息
        List<Case> cases = caseMapper.selectList(new LambdaQueryWrapper<Case>()
                .select(Case::getId, Case::getAmount)
                .in(Case::getId, req.getCaseIds()));
        return assign(cases, req);
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<Void> assignAll(UserInfo userInfo, AssignReq req) {
        List<Case> cases = caseMapper.selectList(new LambdaQueryWrapper<Case>().select(
                Case::getId, Case::getAmount
        ).eq(Case::getClientId, req.getClientId()).eq(Case::getCaseStatus, 1));
        return assign(cases, req);
    }

    private CommResp<Void> assign(List<Case> cases, AssignReq req) {

        if (cases.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "指定的案件不存在");
        }

        // 查询所有相关管理员信息
        List<Admin> admins = adminMapper.selectList(new LambdaQueryWrapper<Admin>()
                .select(Admin::getId, Admin::getNickname)
                .eq(Admin::getAdminRole, 2)
                .in(Admin::getId, req.getAdminIds()));
        if (admins.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "指定的管理员不存在");
        }

        // 根据分配类型进行分配
        Map<Integer, List<Integer>> assignMap = new HashMap<>();
        if (req.getType() == 1) {
            // 按数量平均分配
            assignByCount(cases, admins, assignMap);
        } else if (req.getType() == 2) {
            // 按委托金额分配
            assignByAmount(cases, admins, assignMap);
        } else {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "不支持的分配类型");
        }

        // 执行分配更新
        for (Map.Entry<Integer, List<Integer>> entry : assignMap.entrySet()) {
            Integer adminId = entry.getKey();
            List<Integer> caseIds = entry.getValue();

            Case updateCase = new Case();
            updateCase.setAdminId(adminId);
            updateCase.setCaseStatus((byte) 2);
            LambdaUpdateWrapper<Case> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(Case::getId, caseIds);
            caseMapper.update(updateCase, updateWrapper);
        }

        return CommResp.success(null);
    }

    /**
     * 按数量平均分配案件给管理员
     *
     * @param cases    案件列表
     * @param admins   管理员列表
     * @param assignMap 分配结果映射
     */
    private void assignByCount(List<Case> cases, List<Admin> admins, Map<Integer, List<Integer>> assignMap) {
        // 初始化分配映射
        for (Admin admin : admins) {
            assignMap.put(admin.getId(), new ArrayList<>());
        }

        // 按数量轮询分配
        int adminIndex = 0;
        for (Case caseEntity : cases) {
            Integer adminId = admins.get(adminIndex).getId();
            assignMap.get(adminId).add(caseEntity.getId());
            adminIndex = (adminIndex + 1) % admins.size();
        }
    }

    /**
     * 按委托金额分配案件给管理员
     *
     * @param cases    案件列表
     * @param admins   管理员列表
     * @param assignMap 分配结果映射
     */
    private void assignByAmount(List<Case> cases, List<Admin> admins, Map<Integer, List<Integer>> assignMap) {
        // 初始化分配映射
        for (Admin admin : admins) {
            assignMap.put(admin.getId(), new ArrayList<>());
        }

        // 按金额排序（降序）
        List<Case> sortedCases = cases.stream()
                .sorted((c1, c2) -> c2.getAmount().compareTo(c1.getAmount()))
                .toList();

        // 按金额平均分配 - 计算每个管理员应该分配的金额
        Map<Integer, BigDecimal> adminAmountMap = new HashMap<>();
        for (Admin admin : admins) {
            adminAmountMap.put(admin.getId(), BigDecimal.ZERO);
        }

        // 分配案件给金额最少的管理员
        for (Case caseEntity : sortedCases) {
            // 找到当前金额最少的管理员
            Integer targetAdminId = adminAmountMap.entrySet().stream()
                    .min(Map.Entry.comparingByValue(BigDecimal::compareTo))
                    .map(Map.Entry::getKey)
                    .orElseThrow();

            // 分配案件给该管理员
            assignMap.get(targetAdminId).add(caseEntity.getId());
            
            // 更新该管理员的总金额
            adminAmountMap.computeIfPresent(targetAdminId, (k, currentAmount) -> currentAmount.add(caseEntity.getAmount()));
        }
    }

    /**
     * 根据案件ID获取操作记录列表
     *
     * @param caseId 案件ID
     * @return 操作记录列表
     */
    public CommResp<List<LogResp>> getCaseLogs(UserInfo userInfo, Integer caseId) {
        // 如果不是超级管理员，只能查看自己管理的案件的操作记录
        if (userInfo.getAdminRole() == 2) {
            LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Case::getId);
            queryWrapper.eq(Case::getAdminId, userInfo.getAdminId());
            queryWrapper.eq(Case::getId, caseId);
            Case existing = caseMapper.selectOne(queryWrapper);
            if (existing == null) {
                return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
            }
        }
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationLog::getCaseId, caseId);
        queryWrapper.orderByDesc(OperationLog::getCreatedTime);
        List<OperationLog> caseLogs = operationLogMapper.selectList(queryWrapper);

        // 转换为LogResp列表
        List<LogResp> logResps = new ArrayList<>();
        if (!caseLogs.isEmpty()) {
            // 获取所有相关的管理员ID
            List<Integer> adminIds = caseLogs.stream()
                    .map(OperationLog::getAdminId)
                    .distinct()
                    .filter(java.util.Objects::nonNull)
                    .collect(java.util.stream.Collectors.toList());

            // 批量查询管理员信息
            Map<Integer, String> adminMap = Collections.emptyMap();
            if (!adminIds.isEmpty()) {
                LambdaQueryWrapper<Admin> adminQueryWrapper = new LambdaQueryWrapper<>();
                adminQueryWrapper.select(Admin::getId, Admin::getNickname);
                adminQueryWrapper.in(Admin::getId, adminIds);
                List<Admin> admins = adminMapper.selectList(adminQueryWrapper);
                adminMap = admins.stream()
                        .collect(java.util.stream.Collectors.toMap(Admin::getId, Admin::getNickname));
            }

            // 转换为LogResp
            for (OperationLog caseLog : caseLogs) {
                LogResp logResp = ItemConvertor.toLogResp(caseLog);
                logResp.setNickname(adminMap.get(caseLog.getAdminId()));
                logResps.add(logResp);
            }
        }

        return CommResp.success(logResps);
    }

    /**
     * 根据案件ID获取案件详细信息
     *
     * @param userInfo 用户信息
     * @param caseId   案件ID
     * @param type     查询类型 1-查询比当前caseId小的案件 2-查询比当前案件大的案件 其他-查询当前caseId的案件
     * @return 案件详细信息
     */
    public CommResp<DetailResp> detail(UserInfo userInfo, Integer caseId, Byte type) {
        LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(Case::getCaseStatus, -1);
        
        // 如果不是超级管理员，只能查看自己管理的案件
        if (userInfo.getAdminRole() == 2) {
            queryWrapper.eq(Case::getAdminId, userInfo.getAdminId());
        }
        
        // 根据type参数确定查询条件
        if (type != null && type == 1) {
            // 查询比当前caseId小的案件，按ID降序排列，取第一个
            queryWrapper.lt(Case::getId, caseId);
            queryWrapper.orderByDesc(Case::getId);
            queryWrapper.last("LIMIT 1");
        } else if (type != null && type == 2) {
            // 查询比当前caseId大的案件，按ID升序排列，取第一个
            queryWrapper.gt(Case::getId, caseId);
            queryWrapper.orderByAsc(Case::getId);
            queryWrapper.last("LIMIT 1");
        } else {
            // 查询当前caseId的案件
            queryWrapper.eq(Case::getId, caseId);
        }

        // 查询案件详细信息
        Case caseDetail = caseMapper.selectOne(queryWrapper);
        if (caseDetail == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
        }
        DetailResp detailResp = ItemConvertor.toDetailResp(caseDetail);
        Client client = clientMapper.selectById(caseDetail.getClientId());
        detailResp.setClientName(client.getClientName());
        return CommResp.success(detailResp);
    }
    
    public CommResp<Void> updateStatus(Integer caseId, Byte status) {
        // 查询案件信息
        Case caseEntity = caseMapper.selectOne(new LambdaQueryWrapper<Case>()
                .select(Case::getCaseStatus, Case::getId)
                .eq(Case::getId, caseId));
        if (caseEntity == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
        }

        // 检查案件当前状态是否为待审核状态（状态值为3）
        if (caseEntity.getCaseStatus() != 3) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "只有待审核状态的案件才能进行回退或完结操作");
        }

        // 验证操作类型：1-回退 2-完结
        if (status != 1 && status != 2) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "无效的操作类型");
        }

        // 更新案件状态
        // 1-回退，案件状态改为处理中(2)；2-完结，案件状态改为已结束(0)
        caseEntity.setCaseStatus(status == 1 ? (byte) 2 : (byte) 0);
        caseMapper.updateById(caseEntity);

        return CommResp.success(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<Void> saveLog(UserInfo userInfo, LogReq req) {
        // 参数校验
        if (req.getCaseId() == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件ID不能为空");
        }
        
        if (req.getContactStatus() == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "跟进状态不能为空");
        }
        
        // 检查案件是否存在，只查询必要的字段
        LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Case::getId, Case::getAdminId, Case::getPhoneNo);
        queryWrapper.eq(Case::getId, req.getCaseId());
        Case caseEntity = caseMapper.selectOne(queryWrapper);
        
        if (caseEntity == null) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
        }
        
        // 如果不是超级管理员，只能操作自己管理的案件
        if (!caseEntity.getAdminId().equals(userInfo.getAdminId())) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "无权限操作该案件");
        }
        Set<String> phoneSet = StrUtils.splitByNonDigit(caseEntity.getPhoneNo());
        phoneSet.add(req.getPhoneNo());
        StringBuilder sb = new StringBuilder();
        for (String phone : phoneSet) {
            sb.append(phone).append(",");
        }

        // 创建操作日志
        OperationLog operationLog = ItemConvertor.toOperationLog(req, userInfo);

        // 保存操作日志
        operationLogMapper.insert(operationLog);
        // 更新案件状态
        caseEntity.setPhoneNo(sb.substring(0, sb.length() - 1));
        caseEntity.setCaseStatus((byte) 3);
        caseEntity.setContactStatus(req.getContactStatus());
        caseMapper.updateById(caseEntity);
        
        return CommResp.success(null);
    }

    /**
     * 批量逻辑删除案件
     *
     * @param userInfo 用户信息
     * @param caseIds  案件ID列表
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<Void> batchDelete(UserInfo userInfo, List<Long> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件ID列表不能为空");
        }

        // 查询案件信息
        LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Case::getId, Case::getAdminId, Case::getCaseStatus);
        queryWrapper.in(Case::getId, caseIds);
        List<Case> caseList = caseMapper.selectList(queryWrapper);

        if (caseList.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
        }

        // 执行批量逻辑删除，将案件状态设置为-1（删除状态）
        Case updateCase = new Case();
        updateCase.setCaseStatus((byte) -1);
        LambdaUpdateWrapper<Case> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Case::getId, caseIds);
        caseMapper.update(updateCase, updateWrapper);

        return CommResp.success(null);
    }

    /**
     * 批量重置案件为未分配状态
     *
     * @param userInfo 用户信息
     * @param caseIds  案件ID列表
     * @return 重置结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<Void> batchReset(UserInfo userInfo, List<Long> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件ID列表不能为空");
        }

        // 查询案件信息
        LambdaQueryWrapper<Case> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Case::getId, Case::getAdminId, Case::getCaseStatus);
        queryWrapper.in(Case::getId, caseIds);
        List<Case> caseList = caseMapper.selectList(queryWrapper);

        if (caseList.isEmpty()) {
            return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), "案件不存在");
        }

        // 执行批量重置，将案件状态设置为1（未分配状态），清空管理员ID
        LambdaUpdateWrapper<Case> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Case::getCaseStatus, 1).set(Case::getAdminId, -1);
        updateWrapper.in(Case::getId, caseIds);
        caseMapper.update(updateWrapper);

        return CommResp.success(null);
    }
}