package com.fuyingedu.chat;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;

import java.util.Collections;

/**
 * Mybatis-Plus代码生成器
 * <a href="https://juejin.cn/post/7153855035896512526">使用指南</a>
 * <AUTHOR>
 */
public class CodeGenerator {

    private static final String parentPackage = "my.case0";
    private static final String moduleName = "";
    private static final String url = "********************************************";
    private static final String username = "case_admin";
    private static final String password = "$@!#4213RWQErwqe";
    private static final String tablePrefix = "c_";

    public static void main(String[] args) {

        String projectPath = System.getProperty("user.dir");
        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    builder.author("fengbo")
                            .outputDir(projectPath + "/" + moduleName + "src/main/java")
                            .disableOpenDir()
                            .dateType(DateType.TIME_PACK);
                })
                .packageConfig(builder -> {
                    builder.parent(parentPackage)
                            .entity("entity")
                            .mapper("mapper")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/" + moduleName + "/src/main/resources/mapper"))
                            ;
                })
                .strategyConfig((scanner, builder) -> {
                    builder.addInclude(scanner.apply("请输入表名，多个英文逗号分隔？所有输入"))
                            .addTablePrefix(tablePrefix)
                            .entityBuilder()
                            .enableLombok()
                            .enableFileOverride()
                            .enableChainModel()
                            .disableSerialVersionUID()
                            .mapperBuilder()
                            .serviceBuilder()
                            .disable()
                            .convertServiceFileName(entityName -> "")
                            .convertServiceImplFileName(entityName -> "")
                            .controllerBuilder()
                            .disable()
                            .convertFileName(entityName -> "")
                    ;
                })
                .execute();


    }

}
