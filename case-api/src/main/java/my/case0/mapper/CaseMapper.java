package my.case0.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import my.case0.entity.Case;
import my.case0.model.item.ListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 案件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface CaseMapper extends BaseMapper<Case> {
    
    /**
     * 根据查询条件统计案件数量、总金额和总剩余金额
     * @param req 查询条件
     * @return 统计结果
     */
    CaseStatResult statCases(@Param("req") ListReq req);
    
    /**
     * 分页查询案件列表
     * @param req 查询条件
     * @return 案件分页结果
     */
    List<Case> listCases(@Param("req") ListReq req);
}