package my.case0.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Schema(description = "通用响应结果")
@Setter
@Getter
public class CommResp<T> {

    @Schema(description = "状态码", example = "200")
    private final int code;

    @Schema(description = "状态码描述", example = "请求成功")
    private final String message;

    @Schema(description = "响应数据")
    private T data;

    @Schema(description = "分页信息")
    private PageInfo pageInfo;

    public CommResp(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    @Schema(description = "分页信息")
    @Getter
    public static class PageInfo {

        @Schema(description = "当前页码", example = "1")
        private long pageNum;

        @Schema(description = "每页数量", example = "15")
        private long pageSize;

        @Schema(description = "总页数", example = "10")
        private long totalPage;

        @Schema(description = "总记录数", example = "150")
        private long totalRecord;

        public PageInfo() {}

        public PageInfo(long pageNum, long pageSize) {
            this.pageNum = pageNum;
            this.pageSize = pageSize;
        }

        public void setTotalRecord(long totalRecord) {
            this.totalRecord = totalRecord;
            this.totalPage = (totalRecord - 1) / getPageSize() + 1;
        }

        public long getPageNum() {
            return Math.max(pageNum, 1L);
        }

        public long getPageSize() {
            if (pageSize < 1) {
                return 12;
            }
            return pageSize;
        }
    }

    public static CommResp<?> success() {
        return success(null);
    }

    public static <T> CommResp<T> success(T data) {
        return new CommResp<>(RespMeta.SUCCESS.getStatus(), RespMeta.SUCCESS.getMsg(), data);
    }

    public static <T> CommResp<List<T>> success(long current, long size, long total, List<T> data) {
        CommResp.PageInfo pageInfo = new CommResp.PageInfo(current, size);
        pageInfo.setTotalRecord(total);
        CommResp<List<T>> resp = new CommResp<>(RespMeta.SUCCESS.getStatus(), RespMeta.SUCCESS.getMsg(), data);
        resp.setPageInfo(pageInfo);
        return resp;
    }

    public static <T> CommResp<T> success(long current, long size, long total, T data) {
        CommResp.PageInfo pageInfo = new CommResp.PageInfo(current, size);
        pageInfo.setTotalRecord(total);
        CommResp<T> resp = new CommResp<>(RespMeta.SUCCESS.getStatus(), RespMeta.SUCCESS.getMsg(), data);
        resp.setPageInfo(pageInfo);
        return resp;
    }

    public static <T> CommResp<T> success(String msg, T data) {
        return new CommResp<>(RespMeta.SUCCESS.getStatus(), msg, data);
    }

    public static <T> CommResp<T> warning(RespMeta meta) {
        return new CommResp<>(meta.getStatus(), meta.getMsg(), null);
    }

    public static <T> CommResp<T> warning(String msg) {
        return new CommResp<>(5000, msg, null);
    }

    public static <T> CommResp<T> error(String msg) {
        return new CommResp<>(5000, msg, null);
    }

    public static <T> CommResp<T> warning(int status, String msg) {
        return new CommResp<>(status, msg, null);
    }
}
