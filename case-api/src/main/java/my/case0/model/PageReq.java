package my.case0.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "分页请求基类")
@Getter
@Setter
public class PageReq {

    @Schema(description = "当前页码", example = "1", minimum = "1")
    private long pageNum;

    @Schema(description = "每页数量", example = "15", minimum = "1", maximum = "100")
    private long pageSize;

    public long getPageNum() {
        if (pageNum < 1) {
            return 1;
        }
        return pageNum;
    }

    public long getPageSize() {
        if (pageSize < 1) {
            return 15;
        }
        return pageSize;
    }

    public long getOffset() {
        return (getPageNum() - 1) * getPageSize();
    }
}
