package my.case0.model.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Schema(description = "管理员列表响应")
@Getter
@Setter
public class ListResp {

    @Schema(description = "管理员ID", example = "1")
    private Integer id;

    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createdTime;

    @Schema(description = "用户名", example = "系统管理员")
    private String nickname;

    @Schema(description = "账号", example = "admin")
    private String account;

    @Schema(description = "管理员类型", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminRole;

    @Schema(description = "管理员状态", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminStatus;

    @Schema(description = "最后登录时间", example = "2024-01-01T10:00:00")
    private LocalDateTime lastLoginTime;
}