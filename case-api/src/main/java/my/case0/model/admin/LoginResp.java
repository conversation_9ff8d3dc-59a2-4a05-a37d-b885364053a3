package my.case0.model.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "管理员登录响应")
@Getter
@Setter
public class LoginResp {

    @Schema(description = "JWT令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "账号", example = "admin")
    private String account;

    @Schema(description = "昵称", example = "系统管理员")
    private String nickname;

    @Schema(description = "管理员角色", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminRole;
}