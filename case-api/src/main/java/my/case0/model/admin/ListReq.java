package my.case0.model.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import my.case0.model.PageReq;

@Schema(description = "管理员列表查询请求")
@Getter
@Setter
public class ListReq extends PageReq {

    @Schema(description = "用户名（模糊查询）", example = "管理员")
    private String nickname;

    @Schema(description = "账号（模糊查询）", example = "admin")
    private String account;

    @Schema(description = "管理员类型", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminRole;

    @Schema(description = "管理员状态", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminStatus;
}