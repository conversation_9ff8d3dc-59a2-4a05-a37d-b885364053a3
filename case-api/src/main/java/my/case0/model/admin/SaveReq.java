package my.case0.model.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "管理员保存请求")
@Getter
@Setter
public class SaveReq {

    @Schema(description = "管理员ID，新增时为空，编辑时不为空", example = "1")
    private Integer id;

    @Schema(description = "用户名", example = "系统管理员")
    private String nickname;

    @Schema(description = "账号", example = "admin")
    private String account;

    @Schema(description = "密码，新增时必须提供，编辑时可选", example = "123456")
    private String password;

    @Schema(description = "管理员类型", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminRole;

    @Schema(description = "管理员状态", example = "1", type = "integer", allowableValues = {"1", "2"})
    private Byte adminStatus;
}