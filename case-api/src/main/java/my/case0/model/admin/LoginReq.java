package my.case0.model.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "管理员登录请求")
@Getter
@Setter
public class LoginReq {

    @Schema(description = "账号", example = "admin", required = true)
    private String account;

    @Schema(description = "密码", example = "123456", required = true)
    private String password;

    @Schema(description = "验证码标识", example = "captcha123", required = true)
    private String code;

    @Schema(description = "验证码答案", example = "15", required = true)
    private String verifyCode;
}