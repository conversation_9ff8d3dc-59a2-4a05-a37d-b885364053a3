package my.case0.model.item;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AssignReq {

    @Schema(description = "要分配的案件ID列表")
    private List<Integer> caseIds;

    @Schema(description = "案件所属的委托方ID")
    private Integer clientId;

    @Schema(description = "分配给的管理员ID列表")
    private List<Integer> adminIds;

    @Schema(description = "分配类型 1-根据数量分配 2-根据委托金额分配")
    private Byte type;
}