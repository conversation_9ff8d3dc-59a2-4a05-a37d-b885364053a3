package my.case0.model.item;

import my.case0.entity.Case;
import my.case0.entity.OperationLog;
import my.case0.model.UserInfo;
import my.case0.util.DecimalUtils;

public class ItemConvertor {

    public static Case toCase(SaveReq req) {
        Case caseEntity = new Case();
        caseEntity.setId(req.getId());
        caseEntity.setCaseId(req.getCaseId());
        caseEntity.setUsername(req.getUsername());
        caseEntity.setIdCardNo(req.getIdCardNo());
        caseEntity.setPhoneNo(req.getPhoneNo());
        caseEntity.setClientId(req.getClientId());
        
        // 处理BigDecimal类型转换，考虑空值情况
        caseEntity.setAmount(DecimalUtils.toBigDecimal(req.getAmount()));
        
        caseEntity.setOverdueDay(req.getOverdueDay());
        caseEntity.setAccountDay(req.getAccountDay());
        caseEntity.setStartDate(req.getStartDate());
        caseEntity.setEndDate(req.getEndDate());
        
        // 处理BigDecimal类型转换，考虑空值情况
        caseEntity.setLoadAmount(DecimalUtils.toBigDecimal(req.getLoadAmount()));
        
        caseEntity.setProductName(req.getProductName());
        caseEntity.setOverdueDate(req.getOverdueDate());
        
        // 处理BigDecimal类型转换，考虑空值情况
        caseEntity.setSurplusAmount(DecimalUtils.toBigDecimal(req.getSurplusAmount()));
        
        caseEntity.setLoanCompany(req.getLoanCompany());
        caseEntity.setLoanNum(req.getLoanNum());
        caseEntity.setDefaultDate(req.getDefaultDate());
        caseEntity.setCityName(req.getCityName());
        caseEntity.setRepayNum(req.getRepayNum());
        caseEntity.setNorepayNum(req.getNorepayNum());
        caseEntity.setLoadDate(req.getLoadDate());
        caseEntity.setInterestDate(req.getInterestDate());
        caseEntity.setExpireDate(req.getExpireDate());
        caseEntity.setOrderNo(req.getOrderNo());
        return caseEntity;
    }
    
    public static LogResp toLogResp(OperationLog caseLog) {
        LogResp logResp = new LogResp();
        logResp.setCreatedTime(caseLog.getCreatedTime());
        logResp.setCaseId(caseLog.getCaseId());
        logResp.setAdminId(caseLog.getAdminId());
        logResp.setRemark(caseLog.getRemark());
        logResp.setPhoneNo(caseLog.getPhoneNo());
        logResp.setContactStatus(caseLog.getContactStatus());
        logResp.setContacts(caseLog.getContacts());
        return logResp;
    }
    
    public static ItemResp toListResp(Case caseEntity) {
        ItemResp listResp = new ItemResp();
        listResp.setId(caseEntity.getId());
        listResp.setClientId(caseEntity.getClientId());
        listResp.setCaseId(caseEntity.getCaseId());
        listResp.setUsername(caseEntity.getUsername());
        
        // 处理BigDecimal到String的转换，考虑空值情况
        listResp.setAmount(DecimalUtils.toString(caseEntity.getAmount()));
        
        listResp.setOverdueDay(caseEntity.getOverdueDay());
        
        // 处理BigDecimal到String的转换，考虑空值情况
        listResp.setLoadAmount(DecimalUtils.toString(caseEntity.getLoadAmount()));
        
        listResp.setLoadDate(caseEntity.getLoadDate());
        listResp.setExpireDate(caseEntity.getExpireDate());
        listResp.setCaseStatus(caseEntity.getCaseStatus());
        listResp.setContactStatus(caseEntity.getContactStatus());
        // 处理BigDecimal到String的转换，考虑空值情况
        listResp.setSurplusAmount(DecimalUtils.toString(caseEntity.getSurplusAmount()));
        
        return listResp;
    }
    
    public static OperationLog toOperationLog(LogReq req, UserInfo userInfo) {
        OperationLog operationLog = new OperationLog();
        operationLog.setCaseId(req.getCaseId());
        operationLog.setAdminId(userInfo.getAdminId());
        operationLog.setRemark(req.getRemark());
        operationLog.setPhoneNo(req.getPhoneNo());
        operationLog.setContactStatus(req.getContactStatus());
        operationLog.setContacts(req.getContacts());
        return operationLog;
    }

    public static DetailResp toDetailResp(Case caseEntity) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(caseEntity.getId());
        detailResp.setClientId(caseEntity.getClientId());
        detailResp.setCaseId(caseEntity.getCaseId());
        detailResp.setUsername(caseEntity.getUsername());
        detailResp.setIdCardNo(caseEntity.getIdCardNo());
        detailResp.setPhoneNo(caseEntity.getPhoneNo());
        detailResp.setAmount(DecimalUtils.toString(caseEntity.getAmount()));
        detailResp.setOverdueDay(caseEntity.getOverdueDay());
        detailResp.setAccountDay(caseEntity.getAccountDay());
        detailResp.setStartDate(caseEntity.getStartDate());
        detailResp.setEndDate(caseEntity.getEndDate());
        detailResp.setLoadAmount(DecimalUtils.toString(caseEntity.getLoadAmount()));
        detailResp.setProductName(caseEntity.getProductName());
        detailResp.setOverdueDate(caseEntity.getOverdueDate());
        detailResp.setSurplusAmount(DecimalUtils.toString(caseEntity.getSurplusAmount()));
        detailResp.setLoanCompany(caseEntity.getLoanCompany());
        detailResp.setLoanNum(caseEntity.getLoanNum());
        detailResp.setDefaultDate(caseEntity.getDefaultDate());
        detailResp.setCityName(caseEntity.getCityName());
        detailResp.setRepayNum(caseEntity.getRepayNum());
        detailResp.setNorepayNum(caseEntity.getNorepayNum());
        detailResp.setLoadDate(caseEntity.getLoadDate());
        detailResp.setInterestDate(caseEntity.getInterestDate());
        detailResp.setExpireDate(caseEntity.getExpireDate());
        detailResp.setOrderNo(caseEntity.getOrderNo());
        return detailResp;
    }
}