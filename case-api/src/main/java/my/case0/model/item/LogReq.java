package my.case0.model.item;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "案件日志请求")
@Getter
@Setter
public class LogReq {
    /**
     * 案件ID
     */
    @Schema(description = "案件ID", example = "1")
    private Integer caseId;

    /**
     * 跟进状态 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通
     */
    @Schema(description = "跟进状态 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通",
            example = "1", type = "integer")
    private Byte contactStatus;

    /**
     * 联系人
     */
    @Schema(description = "联系人", example = "张三")
    private String contacts;

    @Schema(description = "手机号", example = "13800138000")
    private String phoneNo;

    /**
     * 操作备注
     */
    @Schema(description = "操作备注", example = "客户表示近期会还款")
    private String remark;
}