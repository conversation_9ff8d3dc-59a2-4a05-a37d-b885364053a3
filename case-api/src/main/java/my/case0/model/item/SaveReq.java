package my.case0.model.item;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Schema(description = "案件保存请求")
@Getter
@Setter
public class SaveReq {

    @ExcelIgnore
    @Schema(description = "案件ID，新增时为空，编辑时不为空", example = "1")
    private Integer id;

    @ExcelProperty("ID")
    @Schema(description = "案件ID（外部）", example = "CASE20250804001")
    private String caseId;

    @ExcelProperty("姓名")
    @Schema(description = "客户名称", example = "张三")
    private String username;

    @ExcelProperty("身份证")
    @Schema(description = "身份证号", example = "110101199003077532")
    private String idCardNo;

    @ExcelProperty("手机号")
    @Schema(description = "手机号", example = "13800138000")
    private String phoneNo;

    @ExcelIgnore
    @Schema(description = "委托方ID", example = "1")
    private Integer clientId;

    @ExcelIgnore
    @Schema(description = "委托方名称", example = "某某公司")
    private String clientName;

    @ExcelProperty("委托金额")
    @Schema(description = "委托金额", example = "10000.00")
    private String amount;

    @ExcelProperty("逾期天数")
    @Schema(description = "逾期天数", example = "30")
    private Integer overdueDay;

    @ExcelProperty("账期")
    @Schema(description = "账期", example = "30")
    private String accountDay;

    @ExcelProperty("委案日期")
    @Schema(description = "委案日期", example = "2025-08-04")
    private String startDate;

    @ExcelProperty("退案日期")
    @Schema(description = "退案日期", example = "2025-11-04")
    private String endDate;

    @ExcelProperty("贷款金额")
    @Schema(description = "贷款金额", example = "10000.00")
    private String loadAmount;

    @ExcelProperty("产品名称")
    @Schema(description = "产品名称", example = "个人消费贷款")
    private String productName;

    @ExcelProperty("逾期开始时间")
    @Schema(description = "逾期开始时间", example = "2025-07-04")
    private String overdueDate;

    @ExcelProperty("剩余本金")
    @Schema(description = "剩余本金", example = "8000.00")
    private String surplusAmount;

    @ExcelProperty("贷款机构")
    @Schema(description = "贷款机构", example = "某某银行")
    private String loanCompany;

    @ExcelProperty("贷款期数")
    @Schema(description = "贷款期数", example = "12")
    private String loanNum;

    @ExcelProperty("默认还款日")
    @Schema(description = "默认还款日", example = "15")
    private String defaultDate;

    @ExcelProperty("贷款申请城市名称")
    @Schema(description = "贷款申请城市名称", example = "北京市")
    private String cityName;

    @ExcelProperty("已还期数")
    @Schema(description = "已还期数", example = "5")
    private String repayNum;

    @ExcelProperty("未还期数")
    @Schema(description = "未还期数", example = "7")
    private String norepayNum;

    @ExcelProperty("借款日期")
    @Schema(description = "借款日期", example = "2024-08-04")
    private String loadDate;

    @ExcelProperty("起息日期")
    @Schema(description = "起息日期", example = "2024-08-04")
    private String interestDate;

    @ExcelProperty("到期日期")
    @Schema(description = "到期日期", example = "2025-08-04")
    private String expireDate;

    @ExcelProperty("订单编号")
    @Schema(description = "订单编号", example = "ORDER20250804001")
    private String orderNo;
}