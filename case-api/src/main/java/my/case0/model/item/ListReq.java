package my.case0.model.item;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import my.case0.model.PageReq;

import java.math.BigDecimal;

@Schema(description = "案件列表查询请求")
@Getter
@Setter
public class ListReq extends PageReq {

    @Schema(description = "案件ID（外部）", example = "CASE20250804001")
    private String caseId;

    @Schema(description = "客户名称", example = "张三")
    private String username;

    @Schema(description = "身份证号", example = "110101199003077532")
    private String idCardNo;

    @Schema(description = "手机号", example = "13800138000")
    private String phoneNo;

    @Schema(description = "分配的管理员ID", example = "1")
    private Integer adminId;

    @Schema(description = "案件状态", example = "1")
    private Byte caseStatus;

    @Schema(description = "委托方ID", example = "1")
    private Short clientId;

    /**
     * 跟进状态 0-新录入 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通
     */
    private Byte contactStatus;
    
    @Schema(description = "逾期天数(最小值)")
    private Integer overdueDayMin;
    
    @Schema(description = "逾期天数(最大值)")
    private Integer overdueDayMax;
    
    @Schema(description = "委托金额(最小值)")
    private BigDecimal amountMin;
    
    @Schema(description = "委托金额(最大值)")
    private BigDecimal amountMax;
    
    @Schema(description = "贷款金额(最小值)")
    private BigDecimal loadAmountMin;
    
    @Schema(description = "贷款金额(最大值)")
    private BigDecimal loadAmountMax;
    
    @Schema(description = "剩余本金(最小值)")
    private BigDecimal surplusAmountMin;
    
    @Schema(description = "剩余本金(最大值)")
    private BigDecimal surplusAmountMax;
}