package my.case0.model.item;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class LogResp {


    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 案件ID
     */
    private Integer caseId;

    /**
     * 操作的用户ID
     */
    private Integer adminId;

    private String nickname;

    /**
     * 跟进状态 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通
     */
    private Byte contactStatus;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 操作备注
     */
    private String remark;

    private String phoneNo;
}
