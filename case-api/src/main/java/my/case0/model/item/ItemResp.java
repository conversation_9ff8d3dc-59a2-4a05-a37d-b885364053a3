package my.case0.model.item;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(description = "案件列表项响应")
public class ItemResp {
    @ExcelProperty("案件ID")
    @Schema(description = "案件ID", example = "1")
    private Integer id;

    /**
     * 1-测试公司
     */
    @ExcelProperty("委托方ID")
    @Schema(description = "委托方ID 1-测试公司", example = "1", type = "integer")
    private Integer clientId;

    /**
     * 委托方名称
     */
    @ExcelProperty("委托方名称")
    @Schema(description = "委托方名称", example = "测试公司")
    private String clientName;
    /**
     * 案件ID（外部）
     */
    @ExcelProperty("案件ID（外部）")
    @Schema(description = "案件ID（外部）", example = "CASE20240101001")
    private String caseId;
    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    @Schema(description = "客户名称", example = "张三")
    private String username;
    /**
     * 委托金额
     */
    @ExcelProperty("委托金额")
    @Schema(description = "委托金额", example = "10000.00")
    private String amount;

    /**
     * 逾期天数
     */
    @ExcelProperty("逾期天数")
    @Schema(description = "逾期天数", example = "30")
    private Integer overdueDay;
    /**
     * 贷款金额
     */
    @ExcelProperty("贷款金额")
    @Schema(description = "贷款金额", example = "5000.00")
    private String loadAmount;

    /**
     * 借款日期
     */
    @ExcelProperty("借款日期")
    @Schema(description = "借款日期", example = "2024-01-01")
    private String loadDate;

    /**
     * 到期日期
     */
    @ExcelProperty("到期日期")
    @Schema(description = "到期日期", example = "2024-06-01")
    private String expireDate;

    @ExcelProperty("分配的用户名")
    @Schema(description = "分配的用户名", example = "李四")
    private String assignUserName;

    /**
     * 0-已结束 1-未派案 2-处理中
     */
    @ExcelIgnore
    @Schema(description = "案件状态 0-已结束 1-未派案 2-处理中", example = "2", type = "integer",
            allowableValues = {"0", "1", "2"})
    private Byte caseStatus;

    @ExcelIgnore
    @Schema(description = "跟进状态 0-新录入 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通", example = "1", type = "integer",
            allowableValues = {"0", "1", "2", "3", "4", "5", "6"})
    private Byte contactStatus;

    @ExcelProperty("剩余本金")
    @Schema(description = "剩余本金")
    private String surplusAmount;
}