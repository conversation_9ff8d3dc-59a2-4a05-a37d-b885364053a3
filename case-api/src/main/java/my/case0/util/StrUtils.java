package my.case0.util;

import java.util.*;
import java.util.stream.Collectors;

public class StrUtils {

    public static final List<String> endpoints = List.of("。", "？", "！", "；", "：", ",", ".", "?", "!", ":", ";");

    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * 基于非数字字符进行切割，并生成一个Set集合
     * @param str 待处理的字符串
     * @return 切割后的字符串Set集合
     */
    public static Set<String> splitByNonDigit(String str) {
        if (isEmpty(str)) {
            return new HashSet<>();
        }
        
        // 使用正则表达式 [^0-9]+ 匹配非数字字符作为分隔符
        return Arrays.stream(str.split("[^0-9]+"))
                .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                .collect(Collectors.toSet());
    }

    public static String underlineToCamel(String word) {
        if (word == null) {
            return null;
        }
        String[] split = word.split("_");
        StringBuilder sb = new StringBuilder(word.length());
        for (String s : split) {
            char[] chars = s.toCharArray();
            if(chars[0] >='a' && chars[0] <= 'z'){
                chars[0] -= 32;
            }
            sb.append(chars);
        }
        return sb.toString();
    }

    public static String camelToUnderline(String name) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < name.length(); ++i) {
            char ch = name.charAt(i);
            if (ch >= 'A' && ch <= 'Z') {
                char newChar = (char) (ch + 32);
                if (i > 0) {
                    buf.append('_');
                }
                buf.append(newChar);
            } else {
                buf.append(ch);
            }
        }
        return buf.toString();
    }

    public static void main(String[] args) {
        String str = "11i99冯00";
        Set<String> set = splitByNonDigit(str);
        System.out.println(set);
    }
}
