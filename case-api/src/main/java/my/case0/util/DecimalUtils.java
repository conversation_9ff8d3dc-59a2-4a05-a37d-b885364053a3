package my.case0.util;

import java.math.BigDecimal;

public class DecimalUtils {


    public static BigDecimal toBigDecimal(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static String toString(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }
}
