package my.case0.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 案件操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("c_operation_log")
public class OperationLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 案件ID
     */
    private Integer caseId;

    /**
     * 操作的用户ID
     */
    private Integer adminId;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 跟进状态 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通
     */
    private Byte contactStatus;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 操作备注
     */
    private String remark;
}
