package my.case0.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 管理员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Setter
@ToString
@TableName("c_admin")
@Accessors(chain = true)
public class Admin {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户名
     */
    private String nickname;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 管理员类型 1-超级管理员 2-普通管理员
     */
    private Byte adminRole;

    /**
     * 管理员状态 1-正常 2-锁定
     */
    private Byte adminStatus;

    /**
     * 密码错误次数
     */
    private Integer pwdErrorNum;

    private LocalDateTime lastLoginTime;
}
