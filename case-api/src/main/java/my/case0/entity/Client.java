package my.case0.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Getter
@Setter
@ToString
@TableName("c_client")
@Accessors(chain = true)
public class Client {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String clientName;

    /**
     * 1-正常 2-删除
     */
    private Byte clientStatus;
}
