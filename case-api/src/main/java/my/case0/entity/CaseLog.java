package my.case0.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 案件操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("c_case_log")
public class CaseLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 案件ID
     */
    private Integer caseId;

    /**
     * 操作的用户ID
     */
    private Integer adminId;

    /**
     * 操作类型 1-分配案件
     */
    private Byte operateType;

    /**
     * 操作备注
     */
    private String remark;
}
