package my.case0.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 案件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Getter
@Setter
@ToString
@TableName("c_case")
@Accessors(chain = true)
public class Case {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 1-测试公司
     */
    private Integer clientId;

    /**
     * 分配的管理员
     */
    private Integer adminId;

    /**
     * 0-已结束 1-未派案 2-处理中 3-待审核
     */
    private Byte caseStatus;

    /**
     * 案件ID（外部）
     */
    private String caseId;

    /**
     * 客户名称
     */
    private String username;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 委托金额
     */
    private BigDecimal amount;

    /**
     * 逾期天数
     */
    private Integer overdueDay;

    /**
     * 账期
     */
    private String accountDay;

    /**
     * 委案日期
     */
    private String startDate;

    /**
     * 退案日期
     */
    private String endDate;

    /**
     * 贷款金额
     */
    private BigDecimal loadAmount;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 逾期开始时间
     */
    private String overdueDate;

    /**
     * 剩余本金
     */
    private BigDecimal surplusAmount;

    /**
     * 贷款机构
     */
    private String loanCompany;

    /**
     * 贷款期数
     */
    private String loanNum;

    /**
     * 默认还款日
     */
    private String defaultDate;

    /**
     * 贷款申请城市名称
     */
    private String cityName;

    /**
     * 已还期数
     */
    private String repayNum;

    /**
     * 未还期数
     */
    private String norepayNum;

    /**
     * 借款日期
     */
    private String loadDate;

    /**
     * 起息日期
     */
    private String interestDate;

    /**
     * 到期日期
     */
    private String expireDate;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 跟进状态 0-新录入 1-承诺还款 2-协商还款 3-谈判中 4-空号 5-停机 6-无法接通
     */
    private Byte contactStatus;
}
