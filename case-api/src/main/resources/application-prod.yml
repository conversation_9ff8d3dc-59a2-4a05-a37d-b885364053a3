server:
  port: 443
  ssl:
    enabled: true
    key-store: classpath:case.jks
    key-store-type: J<PERSON>
    key-store-password: QWERTYUIOasdfghjklqwertyuiop
    key-alias: case

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: case_admin
    password: $@!#4213RWQErwqe
    hikari:
      pool-name: hikari
      minimum-idle: 10
      maximum-pool-size: 100
      connection-timeout: 20_000
      connection-test-query: SELECT 1

# SpringDoc OpenAPI 3 配置
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
  show-actuator: false

app:
  auth:
    token: 1234567890

