<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="my.case0.mapper.CaseMapper">
    <select id="statCases" resultType="my.case0.mapper.CaseStatResult">
        SELECT COUNT(*) as total, 
               COALESCE(SUM(amount), 0) as totalAmount, 
               COALESCE(SUM(surplus_amount), 0) as totalSurplusAmount 
        FROM c_case where case_status != -1
        <if test="req.caseId != null and req.caseId != ''">
            AND case_id LIKE CONCAT('%', #{req.caseId}, '%')
        </if>
        <if test="req.username != null and req.username != ''">
            AND username LIKE CONCAT('%', #{req.username}, '%')
        </if>
        <if test="req.idCardNo != null and req.idCardNo != ''">
            AND id_card_no LIKE CONCAT('%', #{req.idCardNo}, '%')
        </if>
        <if test="req.phoneNo != null and req.phoneNo != ''">
            AND phone_no LIKE CONCAT('%', #{req.phoneNo}, '%')
        </if>
        <if test="req.adminId != null">
            AND admin_id = #{req.adminId}
        </if>
        <if test="req.clientId != null">
            AND client_id = #{req.clientId}
        </if>
        <if test="req.caseStatus != null">
            AND case_status = #{req.caseStatus}
        </if>
        <if test="req.contactStatus != null">
            AND contact_status = #{req.contactStatus}
        </if>
        <if test="req.overdueDayMin != null">
            AND overdue_day >= #{req.overdueDayMin}
        </if>
        <if test="req.overdueDayMax != null">
            AND overdue_day &lt;= #{req.overdueDayMax}
        </if>
        <if test="req.amountMin != null">
            AND amount >= #{req.amountMin}
        </if>
        <if test="req.amountMax != null">
            AND amount &lt;= #{req.amountMax}
        </if>
        <if test="req.loadAmountMin != null">
            AND load_amount >= #{req.loadAmountMin}
        </if>
        <if test="req.loadAmountMax != null">
            AND load_amount &lt;= #{req.loadAmountMax}
        </if>
        <if test="req.surplusAmountMin != null">
            AND surplus_amount >= #{req.surplusAmountMin}
        </if>
        <if test="req.surplusAmountMax != null">
            AND surplus_amount &lt;= #{req.surplusAmountMax}
        </if>
    </select>
    
    <select id="listCases" resultType="my.case0.entity.Case">
        SELECT id, case_id as caseId, client_id as clientId,
               username, amount, overdue_day as overdueDay, load_amount as loadAmount,
               load_date as loadDate, expire_date as expireDate, case_status as caseStatus,
               admin_id as adminId, surplus_amount as surplusAmount, created_time as createdTime,
               contact_status as contactStatus
        FROM c_case where case_status != -1
        <if test="req.caseId != null and req.caseId != ''">
            AND case_id LIKE CONCAT('%', #{req.caseId}, '%')
        </if>
        <if test="req.username != null and req.username != ''">
            AND username LIKE CONCAT('%', #{req.username}, '%')
        </if>
        <if test="req.idCardNo != null and req.idCardNo != ''">
            AND id_card_no LIKE CONCAT('%', #{req.idCardNo}, '%')
        </if>
        <if test="req.phoneNo != null and req.phoneNo != ''">
            AND phone_no LIKE CONCAT('%', #{req.phoneNo}, '%')
        </if>
        <if test="req.adminId != null">
            AND admin_id = #{req.adminId}
        </if>
        <if test="req.clientId != null">
            AND client_id = #{req.clientId}
        </if>
        <if test="req.caseStatus != null">
            AND case_status = #{req.caseStatus}
        </if>
        <if test="req.contactStatus != null">
            AND contact_status = #{req.contactStatus}
        </if>
        <if test="req.overdueDayMin != null">
            AND overdue_day >= #{req.overdueDayMin}
        </if>
        <if test="req.overdueDayMax != null">
            AND overdue_day &lt;= #{req.overdueDayMax}
        </if>
        <if test="req.amountMin != null">
            AND amount >= #{req.amountMin}
        </if>
        <if test="req.amountMax != null">
            AND amount &lt;= #{req.amountMax}
        </if>
        <if test="req.loadAmountMin != null">
            AND load_amount >= #{req.loadAmountMin}
        </if>
        <if test="req.loadAmountMax != null">
            AND load_amount &lt;= #{req.loadAmountMax}
        </if>
        <if test="req.surplusAmountMin != null">
            AND surplus_amount >= #{req.surplusAmountMin}
        </if>
        <if test="req.surplusAmountMax != null">
            AND surplus_amount &lt;= #{req.surplusAmountMax}
        </if>
        ORDER BY id DESC
        LIMIT #{req.pageSize} OFFSET #{req.offset}
    </select>
</mapper>