server:
  port: 8088

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: case_admin
    password: $@!#4213RWQErwqe
    hikari:
      pool-name: hikari
      minimum-idle: 10
      maximum-pool-size: 100
      connection-timeout: 20_000
      connection-test-query: SELECT 1

# SpringDoc OpenAPI 3 配置
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    operations-sorter: method
    tags-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  show-actuator: false

app:
  auth:
    token: 1234567890

